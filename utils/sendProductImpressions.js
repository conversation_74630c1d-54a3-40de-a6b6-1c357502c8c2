export default function sendProductImpressions(products, type) {
	const {gtmTrack} =  useGtm();
	
	if (products?.length) {
		let finalItems = [];
		products.forEach((item,index) => {
			finalItems.push(
				{
					id: item.id,
					name: item.title ? item.title : '',
					category: item.category_title ? item.category_title : '',
					brand: item.manufacturer_title ? item.manufacturer_title : '',
					position: index+1,
					price: item.price_custom ? item.price_custom : '',
					action: type ? type : '',
					list: type ? type : '',
				},
			)
		});
		gtmTrack('productImpressions', {
			ecommerce: {
				impressions: finalItems
			}
		}, {custom: true});
	}
}