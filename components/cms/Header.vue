<template>
	<LazyCmsHelloBar />
	<Body :class="{'active-nav': mMenuOpen}" />
	<div class="t-header">
		<div class="wider-wrapper">
			<BaseCmsNav code="main" v-slot="{items}">
				<ul class="top-nav">
					<li v-for="item in items" :key="item.id">
						<NuxtLink :target="item.target_blank != 0 ? '_blank' : null" :to="item.url_without_domain">{{ item.title }}</NuxtLink>
					</li>
				</ul>
			</BaseCmsNav>
			<BaseCmsLabel code="header_contact" tag="div" class="th-contact" />
			<BaseCmsLabel code="social_icons" tag="div" class="th-socials" />
			
			<SearchForm />
			<AuthUserBox />
			<CatalogWishlistWidget />
			<WebshopShoppingCartBtn />

			<div class="logo-cnt">
				<BaseCmsLogo class="logo" id="logo" />
			</div>
			
			<div @click="mobileMenu" class="btn-toggle-nav" :class="{active: mMenuOpen}"><span></span></div>
		</div>
	</div>

	<div class="main-header">
		<div class="wider-wrapper">
			<CmsCategoriesNav />
		</div>
		<BaseCmsLabel code="header_contact" class="top-contact" />
	</div>
</template>

<script setup>
	const {insertBefore, onMediaQuery} = useDom();
	const route = useRoute();
	const mMenuOpen = ref(false);
	let windowOffset = 0;

	watch(
		() => route.fullPath,
		() => {
			mMenuOpen.value = false;
		}
	)

	function mobileMenu() {
		if (mMenuOpen.value == false) {
			windowOffset = window.scrollY;
			mMenuOpen.value = true;
		} else {
			mMenuOpen.value = false;
			setTimeout(function(){
				window.scroll(0,windowOffset);
			},50);
		}
	}

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
		timeout: 100,
		enter: () => {
			insertBefore('.top-nav', '.top-contact');
		},
	});
</script>

<style lang="less" scoped>
	.t-header{
		position: relative; background: var(--primaryColor); color: white; min-height: 50px; max-height:50px;
		.wider-wrapper{position: relative; display: flex; min-height: 50px;}
		@media (max-width: @l){
			min-height: 44px; max-height:44px;
			.wider-wrapper{min-height: 44px;}
		}
		@media (max-width: @t){max-height:unset;}
		@media (max-width: @tp){
			min-height: 56px;
			.wider-wrapper{min-height: 56px; margin: 0;}
		}
		@media (max-width: @m){
			.wider-wrapper{margin: 0;}
		}
	}
	:deep(.top-nav){
		display: flex; list-style-type: none; align-items: center; margin-left: 205px;
		li{
			font-size: 14px; line-height: 50px;
		}
		li{
			ul{display: none;}
		}
		a{
			display: block; text-decoration: none; color: white; padding: 0 8px;
			@media(min-width: @h){
				&:hover{background: #472266;}
			}
		}
		@media (max-width: @l){
			margin-left: 195px;
			li{font-size: 13.5px; line-height: 44px;}
			a{padding: 0 4px;}
		}
		@media (max-width: @t){
			margin-left: 140px; flex-grow: 1;
			li{font-size: 13px; line-height: 44px; flex-grow: 1;}
			a{padding: 0 4px;}
		}
		@media (max-width: @tp){
			display: none; left: auto; width: 100%; background: var(--primaryColor); padding: 20px 0; position: relative;
			li{width: 50%; line-height: 1.2; font-size: 14px;}
			a{padding: 4px 15px;}
		}
		@media (max-width: @m){display: none;}
	}

	:deep(.th-contact){
		font-size: 0; line-height: 0; margin-left: 15px; margin-right: 15px; flex-grow: 1;
		a{display: none;}
		a[href^=tel]{display: block !important; font-size: 14px !important; line-height: 21px !important; text-decoration: none; position: relative; color: white; padding-left: 20px; height: 50px; display: flex !important; align-items: center; justify-content: center;
			&:before{.pseudo(auto,auto); .icon-phone; font: 18px/18px var(--fonti); left: 0; top: 15px; color: white;}
			@media(min-width: @h){
				&:hover{color: var(--white)/1.2;}
			}
		}
		@media (max-width: @l){
			margin-left: 10px; margin-right: 0;
			a[href^=tel]{font-size: 15px !important; line-height: 21px !important; padding-left: 25px; height: 44px; justify-content: unset;
				&:before{font: 16px/16px var(--fonti); top: 14px; left: 0;}
			}
		}
		@media (max-width: @t){display: none;}
	}

	:deep(.th-socials){
		display: flex;
		a{width: 45px; height: 50px; position: relative; display: flex; align-items: center; justify-content: center; border-left: 1px solid rgba(255,255,255,0.2); font-size: 0; line-height: 0; .transition(background);
			&:after{.pseudo(auto,auto); .icon-fb; font: 16px/16px var(--fonti); color: white;}
			@media(min-width: @h){
				&:hover{background: #472266;}
			}
		}
		.in:after{.icon-in; font:17px/17px var(--fonti);}
		.yt:after{.icon-youtube; font:17px/17px var(--fonti);}
		.tt{
			border-right: 1px solid rgba(255,255,255,0.2);
			&:after{.icon-tiktok; font: 16px/16px var(--fonti);}
		}
		@media (max-width: @l){
			a{
				width: 44px; height: 44px;
				&:after{font: 15px/15px var(--fonti);}
			}
			.in:after{font: 16px/16px var(--fonti);}
		}
		@media (max-width: @t){display: none;}
	}

	//TOOGLE NAV
	.btn-toggle-nav{
		@media (max-width: @tp){
			display: block; position: absolute; top: 0; left: 0; width: 56px; height: 56px; z-index: 110;
			span, span:after, span:before { .pseudo(20px,1px); background: #fff; left: 0; top: 0; .transition(all); }
			span {
				top: 28px; left: 18px;
				&:before {top: -6px;}
				&:after {top: 6px;}
			}
			&.active {
				span {background: transparent;}
				span:after {.rotate(45deg); top: 0;}
				span:before {.rotate(-45deg); top: 0;}
			}
		}
	}


	//MAIN HEADER
	.main-header{
		min-height: 84px; background: white; position: relative;
		.wider-wrapper{position: relative; min-height: 84px;}
		@media (max-width: @t){
			min-height: 55px;
 			.wider-wrapper{min-height: 55px;}
		}
		@media (max-width: @tp){display: none;}
	}
	.top-contact{
		display: none;
		@media (max-width: @m){
			display: block; color: #fff; border-top: 1px solid rgba(255,255,255,.2); text-align: center; font-size: 13px; padding: 20px 0;
			a{color: #fff; text-decoration: none;}
			a[href^=tel]{color: #fff;}
		}
	}
</style>

<style lang="less">
	@media (max-width: @tp) {
		.active-nav{
			.main-slider,.benefits,.hp-promos,.special-products,.hp-brands,.hp-events,.hp-landing-categories,.hp-blog,.newsletter-loyalty,.footer,.intro-container,.main,.benefits,.about-image,.first-row,.about-second-image,.second-row,.contact-wrapper,.mapContainer,.faq-wrapper,.featured-brands,.m-items,.publish,.publish-detail,.pd-comments,.pd-related,.catalog-layout,.product-detail,.cd-special-products,.cart-layout,.search-layout,.login-register,.main, .wishlist-section, .a-wishlist-header, .wishlist-detail-items, .auth-no-content, .hello-bar,.nuxt-page-container,.sw-intro,.sw-main{width: 0; height: 0!important; overflow: hidden !important; min-width: 0; min-height: 0; padding: 0 !important; margin: 0 !important; display: none;}
			//.page-wrapper>*:not(.t-header):not(.main-header){display: none;}
			.t-header{position: relative;}
			.wwk-sidebar{display: none !important;}
			.to-top{display: none;}
			.page-wrapper{position: absolute; top: 0; right: 0; bottom: 0; left: 0;}
			.main-header{display: block; position: absolute; top: 65px; right: 0; bottom: 0; left: 0; overflow: auto; background: white;}
			.top-nav{display: flex; flex-wrap: wrap; margin-left: 0;}
			.wider-wrapper{margin: 0;}
			.top-contact{font-size: 0; line-height: 0; display: flex; flex-flow: column; align-items: center; background: var(--primaryColor);
				a{font-size: 13px !important; line-height: 20px; color: white; text-decoration: none;}
				a[href^=mailto]{text-decoration: underline !important;}
			}
			//.nav-bestsellers-cnt{display: none;}
		}
	}
</style>