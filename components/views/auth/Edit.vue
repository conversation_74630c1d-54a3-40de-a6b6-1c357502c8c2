<template>
	<BaseCmsPage v-slot="{page}">
		<CmsIntroContainer :page="page" mode="auth">
			<template #h1>
				<h1 v-if="page?.title">{{page.title}}</h1>
			</template>
		</CmsIntroContainer>
		<AuthMainLayout>
			<template #authContent>
				<ClientOnly>
					<BaseAuthEditForm class="auth-form auth-edit-profile-form ajax_siteform_loading form form-animated-label" v-slot="{fields, loading, status}" >
						<template v-if="status?.data?.errors?.length">
							<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.field }}: {{ error.error }}</div>
						</template>
						<div v-if="status?.success" class="global-success"><BaseCmsLabel :code="status.data.label_name" /></div>
						<fieldset>
							<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value, required}">
								<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
									<BaseFormInput :id="item.name" />
									<label :for="item.name"><BaseCmsLabel :code="item.name == 'location' ? 'zipcode_city' : item.name" :default="item.name" /><template v-if="required && item.name != 'newsletter'">* </template></label>
									<span class="error" v-show="errorMessage" v-html="errorMessage" />
								</p>
							</BaseFormField>
						</fieldset>
						<div class="lr-btn-pw">
							<button class="btn btn-primary g-recaptcha" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="save_auth_edit" tag="span" /></button>
							<BaseCmsLabel code="required_fields" class="req" tag="p" />
						</div>
					</BaseAuthEditForm>
				</ClientOnly>
			</template>
		</AuthMainLayout>
	</BaseCmsPage>
</template>

<script setup>
	 const labels = useLabels();
</script>

<style lang="less" scoped>
	.field-zipcode, .field-city{display: none;}
	.lr-btn-pw{
		@media (max-width: @t){
			:deep(.btn){margin-right: 10px; margin-bottom: 10px;}
		}
		@media (max-width: @m){
			flex-flow: column-reverse;
			:deep(.btn){margin-right: 0; margin-top: 10px; width: 100%;}
			.req{padding-right: 0;}
		}
	}
	.field-newsletter{
		@media (max-width: @d){
			input[type=checkbox] + label{font-size: 16px;}
		}
		@media (max-width: @m){
			input[type=checkbox] + label{font-size: 14px;}
		}
	}
</style>